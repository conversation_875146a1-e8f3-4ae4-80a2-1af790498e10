import { gql } from '__generated__/gql';





export const CREATE_OR_UPDATE_FIELD_EVENT = gql(/* GraphQL */ `
  mutation createOrUpdateFieldEvent(
    $fieldId: ID!
    $projectId: ID!
    $phaseId: ID!
    $event: FieldEventInput!
  ) {
    createOrUpdateFieldEvent(
      fieldId: $fieldId
      projectId: $projectId
      phaseId: $phaseId
      event: $event
    ) {
      id
      type
      event_values {
        ...FallowPeriodAttributes
        ...CroppingEventAttributes
        ...TillageEventAttributes
        ...IrrigationEventAttributes
        ...ApplicationEventAttributes
      }
    }
  }
`);

export const BULK_CREATE_OR_UPDATE_FIELD_EVENTS = gql(/* GraphQL */ `
  mutation bulkCreateOrUpdateFieldEvents($events: [BulkFieldEventInput!]!) {
    bulkCreateOrUpdateFieldEvents(events: $events) {
      events {
        id
        type
        event_values {
          ...FallowPeriodAttributes
          ...CroppingEventAttributes
          ...TillageEventAttributes
          ...IrrigationEventAttributes
          ...ApplicationEventAttributes
        }
      }
    }
  }
`);

export const DELETE_FIELD_EVENT = gql(/* GraphQL */ `
  mutation deleteFieldEvent(
    $projectId: ID!
    $phaseId: ID!
    $fieldId: ID!
    $event: FieldEventDeleteInput!
  ) {
    deleteFieldEvent(projectId: $projectId, phaseId: $phaseId, fieldId: $fieldId, event: $event)
  }
`);

export const UPDATE_NO_PRACTICE_OBSERVATION = gql(/* GraphQL */ `
  mutation updateCultivationCycleNoPracticeObservation(
    $projectId: ID!
    $phaseId: ID!
    $stageId: ID!
    $fieldId: ID!
    $cultivationCycleId: ID!
    $noPracticeObservationValue: Boolean!
  ) {
    updateCultivationCycleNoPracticeObservation(
      projectId: $projectId
      phaseId: $phaseId
      stageId: $stageId
      fieldId: $fieldId
      cultivationCycleId: $cultivationCycleId
      noPracticeObservationValue: $noPracticeObservationValue
    ) {
      id
      start_date
      end_date
      crop_type
      no_practice_observations {
        tillage_event
        irrigation_event
        application_event
      }
    }
  }
`);

export const BULK_COPY_FIELD_EVENTS = gql(/* GraphQL */ `
  mutation bulkCopyFieldEvents(
    $events: [BulkCopyFieldEventInput!]!
    $copyMode: CopyFieldEventsMode!
  ) {
    bulkCopyFieldEvents(events: $events, copyMode: $copyMode) {
      id
      type
    }
  }
`);