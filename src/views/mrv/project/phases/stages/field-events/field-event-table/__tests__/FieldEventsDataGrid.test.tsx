import {fireEvent, screen, waitFor, within} from '@testing-library/react';
import {enUS} from 'date-fns/locale';
import uniqueId from 'lodash/uniqueId';
import React from 'react';

import {FilledInput} from '@regrow-internal/design-system';

import {useAppSelector} from 'store/useRedux';

import type {CroppingEvent} from '__generated__/gql/graphql';
import type {FieldEvent} from '__generated__/mrv/mrvApi.types';
import {AttributeTypes, StageTypes} from '__generated__/mrv/mrvApi.types';
import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {useHasPermissions} from 'containers/mrv/_hooks/useHasPermissions';
import {EPermissions} from 'containers/mrv/types';
import {mockCropEventsStage} from 'views/mrv/project/phases/__tests__/graphqlMocks';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {mockStageContext} from 'views/mrv/project/phases/stages/__mocks__/StageContext';
import {GET_PHASE_CULTIVATION_CYCLE_EVENTS_LOCKED_MOCK} from 'views/mrv/project/phases/stages/__tests__/mock-data/graphqlMocks';
import {
  cropAttributeOptions,
  croppingEvents,
  fieldsWithCultivationCycles,
  mockSelectedField,
} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {mockFieldEventContext} from 'views/mrv/project/phases/stages/field-events/__mocks__/FieldEventContext';
import {FieldEventsDataGrid} from 'views/mrv/project/phases/stages/field-events/field-event-table/FieldEventsDataGrid';
import {ErrorToolTipWrapper} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/ErrorToolTipWrapper';
import type {FieldEventGridColDef} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/types';
import {getColumn} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/utils';
import {useFieldEventContext} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';
import {convertStringToDate} from 'views/mrv/project/phases/stages/field-events/utils/dateUtils';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

jest.mock('lodash/uniqueId');
jest.mock('containers/mrv/_hooks/useNavigateToStage', () => ({
  useNavigateToStage: jest.fn().mockReturnValue({
    navigateToStage: jest.fn(),
  }),
}));
jest.mock('containers/mrv/_hooks/useFeatures');
jest.mock('containers/app/CurrentUserContext');
jest.mock('views/mrv/project/phases/stages/field-events/FieldEventContext');
jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('containers/mrv/_hooks/useHasPermissions');
jest.mock(
  'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/CopyFieldEventsDialog',
  () => ({
    CopyFieldEvents: () => <button>Copy field events button</button>,
  })
);
jest.mock('store/useRedux', () => ({
  ...jest.requireActual('store/useRedux'),
  useAppSelector: jest.fn(),
}));

jest.mock('_translations/utils', () => ({
  ...jest.requireActual('_translations/utils'),
  useDateFnsLocale: () => enUS,
}));

const initialMockId = 'initial-mock-unique-id';
const mockUniqueId = uniqueId as jest.MockedFunction<typeof uniqueId>;
mockUniqueId.mockReturnValue(initialMockId);

const selectedField = fieldsWithCultivationCycles(croppingEvents)[0];
const eventRowCount = selectedField?.cultivation_cycles?.flatMap(
  ({events}) => events?.flatMap(e => e)
)?.length;
const firstEvent = selectedField?.cultivation_cycles?.[0]?.events?.[0];

const useFieldEventContextState = {
  ...mockFieldEventContext,
  createOrUpdateEvent: jest
    .fn()
    .mockResolvedValue({data: {createFieldEvent: {}, errors: undefined}}),
  deleteFieldEvent: jest.fn(),
  updateNoPracticeObservation: jest.fn(),
  fieldCultivationCycles: selectedField?.cultivation_cycles,
  bulkCreateOrUpdateLoading: false,
};

const useStageContextState = {
  ...mockStageContext,
  attributeOptions: cropAttributeOptions,
  currentStage: mockCropEventsStage,
};

const formatMessage = ({defaultMessage}: any) => defaultMessage;
const mockValidator = jest.fn().mockImplementation(() => null);

const mockColumn: FieldEventGridColDef = {
  ...getColumn({
    attributeType: AttributeTypes.CropYield,
    stageType: StageTypes.CROP_EVENTS,
    formatMessage,
    validator: mockValidator,
  }),
  headerName: 'Mock table column',
  renderEditCell: ({value, api, id, field, error}) => {
    const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      void api.setEditCellValue({id, field, value: newValue});
    };

    return (
      <ErrorToolTipWrapper error={error}>
        <FilledInput
          type="number"
          inputProps={{'aria-label': AttributeTypes.CropYield}}
          defaultValue={value}
          onChange={handleValueChange}
          error={error}
          fullWidth
        />
      </ErrorToolTipWrapper>
    );
  },
};

describe('FieldEventsDataGrid', () => {
  let test: any;

  beforeAll(() => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Idle',
              error: null,
              prefilledFields: [],
            },
          },
        },
      })
    );
  });

  beforeEach(() => {
    test = {};
    test.props = {columns: [mockColumn]};

    test.useFieldEventContext = useFieldEventContext as jest.MockedFunction<
      typeof useFieldEventContext
    >;
    test.useFieldEventContext.mockReturnValue(useFieldEventContextState);
    (useStageContext as jest.MockedFunction<typeof useStageContext>).mockReturnValue(
      useStageContextState
    );
    test.usePhaseContext = usePhaseContext as jest.MockedFunction<typeof usePhaseContext>;
    test.useHasPermissions = useHasPermissions as jest.MockedFunction<typeof useHasPermissions>;
    test.useHasPermissions.mockReturnValue([false]);
  });

  it('should render a row for headers and a row for each event', async () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

    await waitFor(() => {
      expect(
        screen
          .getByLabelText(`Field ${selectedField?.id} crop events table`)
          .getAttribute('aria-rowcount')
      ).toEqual((Number(eventRowCount) + 1).toString());
    });
  });

  it('should render cell columns with cultivation cycle and actions', () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    expect(selectedField?.cultivation_cycles[0]?.events.length).toBeGreaterThan(0);
    selectedField?.cultivation_cycles.forEach(({events}) => {
      events?.forEach((e: FieldEvent, index: number) => {
        const event = e.event_values as CroppingEvent;
        const eventRow = screen
          .getAllByRole('row')
          .find(row => row.getAttribute('data-id') === e?.id);

        if (!eventRow) {
          throw new Error('Event row not found');
        }

        const cultCycleHeading = `${event.crop_type} ${convertStringToDate(
          event?.harvest_date
        )?.getFullYear()}`;

        if (index === 0) {
          expect(
            within(eventRow).getByRole('heading', {
              name: cultCycleHeading,
            })
          ).toBeInTheDocument();
        } else {
          expect(
            within(eventRow).queryByRole('heading', {
              name: cultCycleHeading,
            })
          ).not.toBeInTheDocument();
        }

        expect(
          within(eventRow).getByRole('cell', {name: String(event?.crop_yield)})
        ).toBeInTheDocument();

        const actions = within(eventRow).getByRole('menu');
        expect(
          within(actions).getByRole('menuitem', {name: `Edit event ${e.id}`})
        ).toBeInTheDocument();
        expect(
          within(actions).getByRole('menuitem', {name: `Delete event ${e.id}`})
        ).toBeInTheDocument();
      });
    });
  });

  it('should not set no practice observation column for cropping stage', () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    expect(
      within(eventRow).queryByRole('checkbox', {name: /no practice observation/})
    ).not.toBeInTheDocument();
  });

  it('should set no practice observation column for field practices stage', async () => {
    (useStageContext as jest.MockedFunction<typeof useStageContext>).mockReturnValue({
      ...useStageContextState,
      currentStage: {
        ...mockCropEventsStage,
        type: StageTypes.TILLAGE_EVENTS,
        name: 'Tillage',
      },
    });
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    const npoSwitchName = `tillage_event no practice observation for ${selectedField?.cultivation_cycles?.[0]?.crop_type} ${selectedField?.cultivation_cycles?.[0]?.end_date}`;
    const npoSwitch = within(eventRow).getByRole('checkbox', {
      name: npoSwitchName,
    });
    expect(npoSwitch).toBeChecked();
    fireEvent.click(npoSwitch);
    expect(
      within(eventRow).getByRole('checkbox', {
        name: npoSwitchName,
      })
    ).not.toBeChecked();

    await waitFor(() => {
      expect(useFieldEventContextState.updateNoPracticeObservation).toHaveBeenCalledWith(
        selectedField?.cultivation_cycles?.[0]?.id,
        true
      );
    });
  });

  it('should add a new unsaved event row with disabled save btn + tooltip', () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

    const initialRowCount = Number(eventRowCount) + 1;
    expect(
      screen
        .getByLabelText(`Field ${selectedField?.id} crop events table`)
        .getAttribute('aria-rowcount')
    ).toEqual(initialRowCount.toString());

    fireEvent.click(screen.getByRole('button', {name: 'Add event'}));

    expect(
      screen
        .getByLabelText(`Field ${selectedField?.id} crop events table`)
        .getAttribute('aria-rowcount')
    ).toEqual((initialRowCount + 1).toString());

    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === initialMockId);

    if (!eventRow) {
      throw new Error('New event row not found');
    }

    const actions = within(eventRow).getByRole('menu');

    expect(
      within(actions).getByRole('menuitem', {name: `Cancel edit event ${initialMockId}`})
    ).toBeInTheDocument();

    const saveEvent = within(actions).getByRole('menuitem', {name: `Save event ${initialMockId}`});
    expect(saveEvent).toBeInTheDocument();
    expect(saveEvent).toBeDisabled();
    fireEvent.mouseEnter(saveEvent);
    expect(screen.getByRole('tooltip')).toHaveTextContent('Missing required data to save event');
  });

  it('should delete an event', () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    fireEvent.click(
      within(eventRow).getByRole('menuitem', {name: `Delete event ${firstEvent.id}`})
    );

    const cultivationCycle = selectedField?.cultivation_cycles?.[0];

    const deleteEventRow = {
      id: firstEvent.id,
      __typename: firstEvent.type,
      ...firstEvent.event_values,
      meta: {
        cultivationCycle: {
          ...cultivationCycle,
          events: undefined,
        },
        eventCount: cultivationCycle?.events?.length,
        showCultivationCycle: true,
        type: firstEvent.type,
        isLocked: undefined,
        isReadOnly: false,
      },
    };

    expect(useFieldEventContextState.deleteFieldEvent).toHaveBeenCalledWith(
      selectedField?.id,
      deleteEventRow
    );
  });

  it('should render a locked row if events are locked', () => {
    const selectedFieldWithLocked =
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_LOCKED_MOCK.result.data.mrv.project.stage.fields[0];

    const useFieldEventContextStateWithLocked = {
      ...useFieldEventContextState,
      fieldCultivationCycles: selectedFieldWithLocked?.cultivation_cycles,
    };

    const firstLockedEvent = selectedFieldWithLocked?.cultivation_cycles?.[0]?.events?.[0];

    test.useFieldEventContext.mockReturnValue(useFieldEventContextStateWithLocked);

    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstLockedEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }
    const cropYield = within(eventRow).getByRole('cell', {
      name: String(firstLockedEvent.event_values.crop_yield),
    });
    fireEvent.doubleClick(cropYield);

    expect(within(eventRow).getByRole('presentation').title).toBe(
      String(firstLockedEvent.event_values.crop_yield)
    );

    const actions = within(eventRow).getByRole('menu');

    expect(
      within(actions).getByRole('menuitem', {name: `Row locked ${firstLockedEvent.id}`})
    ).toBeInTheDocument();

    expect(
      within(eventRow).queryByRole('menuitem', {name: `Save event ${initialMockId}`})
    ).not.toBeInTheDocument();

    expect(
      within(eventRow).queryByRole('menuitem', {name: `Delete event ${firstLockedEvent.id}`})
    ).not.toBeInTheDocument();
  });

  it('should render a loading state when monitor prefill is loading', () => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Loading',
              error: null,
              prefilledFields: [{md5: 'md5', status: 'processing'}],
            },
          },
        },
      })
    );

    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    expect(screen.getByLabelText('field events table loading')).toBeInTheDocument();
  });

  it('should render a loading state when monitor prefill field is currently loading', () => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Success',
              error: null,
              prefilledFields: [{md5: 'md5', status: 'processing'}],
            },
          },
        },
      })
    );

    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    expect(screen.getByLabelText('field events table loading')).toBeInTheDocument();
  });

  it('should rendering a circular progress when row is loading', async () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    const cropYield = within(eventRow).getByRole('cell', {
      name: String(firstEvent.event_values.crop_yield),
    });
    fireEvent.doubleClick(cropYield);

    const actions = within(eventRow).getByRole('menu');
    const saveEvent = within(actions).getByRole('menuitem', {name: `Save event ${firstEvent.id}`});
    expect(saveEvent).toBeInTheDocument();

    fireEvent.click(saveEvent);
    await waitFor(() => {
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  it('should disable save all button when there are no saveable edits', () => {
    useFieldEventContextState.bulkCreateOrUpdateLoading = false;
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    expect(screen.getByRole('button', {name: 'Save all'})).toBeDisabled();
  });

  it('should enable save all button when there are saveable edits', () => {
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    const cropYield = within(eventRow).getByRole('cell', {
      name: String(firstEvent.event_values.crop_yield),
    });
    fireEvent.doubleClick(cropYield);

    expect(screen.getByRole('button', {name: 'Save all'})).toBeEnabled();
  });

  it('should render a locked row when phase is locked', () => {
    test.usePhaseContext.mockReturnValue({
      phaseIsReadOnly: true,
    });
    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstEvent.id);

    if (!eventRow) {
      throw new Error('Event row not found');
    }

    const cropYield = within(eventRow).getByRole('cell', {
      name: String(firstEvent.event_values.crop_yield),
    });

    fireEvent.doubleClick(cropYield);

    const actions = within(eventRow).getByRole('menu');

    expect(
      within(actions).getByRole('menuitem', {name: `Row locked ${firstEvent.id}`})
    ).toBeInTheDocument();

    expect(
      within(eventRow).queryByRole('menuitem', {name: `Save event ${initialMockId}`})
    ).not.toBeInTheDocument();

    expect(
      within(eventRow).queryByRole('menuitem', {name: `Delete event ${firstEvent.id}`})
    ).not.toBeInTheDocument();
  });

  it('should allow editing locked events when user has BYPASS_PROJECT_FIELD_EVENTS_LOCK permission', () => {
    // Mock the useHasPermissions hook to return true for the bypass permission
    test.useHasPermissions.mockReturnValue([true]);

    const selectedFieldWithLocked =
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_LOCKED_MOCK.result.data.mrv.project.stage.fields[0];

    const useFieldEventContextStateWithLocked = {
      ...useFieldEventContextState,
      fieldCultivationCycles: selectedFieldWithLocked?.cultivation_cycles,
    };

    const firstLockedEvent = selectedFieldWithLocked?.cultivation_cycles?.[0]?.events?.[0];
    test.useFieldEventContext.mockReturnValue(useFieldEventContextStateWithLocked);

    renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);
    expect(test.useHasPermissions).toHaveBeenCalledWith([
      EPermissions.BYPASS_PROJECT_FIELD_EVENTS_LOCK,
    ]);

    const eventRow = screen
      .getAllByRole('row')
      .find(row => row.getAttribute('data-id') === firstLockedEvent.id);
    const cropYield = within(eventRow!).getByRole('cell', {
      name: String(firstLockedEvent.event_values.crop_yield),
    });

    fireEvent.doubleClick(cropYield);
    const actions = within(eventRow!).getByRole('menu');
    expect(
      within(actions).queryByRole('menuitem', {name: `Row locked ${firstLockedEvent.id}`})
    ).not.toBeInTheDocument();

    expect(
      within(actions).getByRole('menuitem', {name: `Save event ${firstLockedEvent.id}`})
    ).toBeInTheDocument();

    expect(
      within(actions).getByRole('menuitem', {name: `Cancel edit event ${firstLockedEvent.id}`})
    ).toBeInTheDocument();
  });

  describe('Row Selection with Checkboxes', () => {
    let mockSetSelectedFieldEvents: jest.Mock;
    let mockClearSelectedFieldEvents: jest.Mock;

    beforeEach(() => {
      mockSetSelectedFieldEvents = jest.fn();
      mockClearSelectedFieldEvents = jest.fn();

      test.useSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
        typeof useSelectedFieldContext
      >;

      test.useSelectedFieldContext.mockReturnValue({
        ...mockSelectedFieldContext,
        setSelectedFieldEvents: mockSetSelectedFieldEvents,
        clearSelectedFieldEvents: mockClearSelectedFieldEvents,
        selectedFieldEvents: {
          field: null,
          events: null,
        },
      });
    });

    it('should call setSelectedFieldEvents when rows are selected', async () => {
      renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

      const rowCheckboxes = screen.getAllByRole('checkbox').slice(1);
      expect(rowCheckboxes.length).toBeGreaterThan(1);

      const firstCheckbox = rowCheckboxes[0];
      if (!firstCheckbox) {
        throw new Error('First checkbox not found');
      }

      expect(firstCheckbox).toBeEnabled();
      fireEvent.click(firstCheckbox);
      await waitFor(() => {
        expect(mockSetSelectedFieldEvents).toHaveBeenCalledWith([firstEvent.id], 'CroppingEvent');
      });

      const secondCheckbox = rowCheckboxes[1];
      if (!secondCheckbox) {
        throw new Error('Second checkbox not found');
      }
      fireEvent.click(secondCheckbox);
      await waitFor(() => {
        expect(mockSetSelectedFieldEvents).toHaveBeenCalledWith(
          expect.arrayContaining([expect.any(String)]),
          'CroppingEvent'
        );
      });
    });

    it('should handle select all checkbox functionality', async () => {
      renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

      const headerCheckbox = screen.getAllByRole('checkbox')[0];
      expect(headerCheckbox).toBeInTheDocument();
      if (!headerCheckbox) {
        throw new Error('Header checkbox not found');
      }
      fireEvent.click(headerCheckbox);

      const eventIds = selectedField?.cultivation_cycles?.flatMap(cycle =>
        cycle.events.map(e => e.id)
      );
      await waitFor(() => {
        expect(mockSetSelectedFieldEvents).toHaveBeenCalledWith(
          expect.arrayContaining(eventIds!),
          'CroppingEvent'
        );
      });
    });

    it('should not allow row selection when field is different from selected field', () => {
      const mockSelectedFieldContextWithDifferentField = {
        ...mockSelectedFieldContext,
        setSelectedFieldEvents: mockSetSelectedFieldEvents,
        clearSelectedFieldEvents: mockClearSelectedFieldEvents,
        selectedFieldEvents: {
          field: {id: 'different-field-id', name: 'Different Field'},
          events: {CroppingEvent: ['some-id']},
        },
        selectedField: mockSelectedField,
      };

      test.useSelectedFieldContext.mockReturnValue(mockSelectedFieldContextWithDifferentField);

      renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

      const rowCheckboxes = screen.getAllByRole('checkbox').slice(1);
      rowCheckboxes.forEach(checkbox => {
        expect(checkbox).toBeDisabled();
      });
    });

    it('should render selected events snackbar and clear selection when close button is clicked', () => {
      const mockSelectedFieldContextWithSelection = {
        ...mockSelectedFieldContext,
        setSelectedFieldEvents: mockSetSelectedFieldEvents,
        clearSelectedFieldEvents: mockClearSelectedFieldEvents,
        selectedFieldEvents: {
          field: mockSelectedField,
          events: {CroppingEvent: [firstEvent.id]},
        },
      };
      test.useSelectedFieldContext.mockReturnValue(mockSelectedFieldContextWithSelection);

      renderWithSimpleProviders(<FieldEventsDataGrid {...test.props} />);

      const rowCheckboxes = screen.getAllByRole('checkbox').slice(1);
      const firstCheckbox = rowCheckboxes[0];
      if (!firstCheckbox) {
        throw new Error('First checkbox not found');
      }
      expect(firstCheckbox).toBeChecked();

      expect(screen.getByRole('presentation', {name: 'selected events panel'})).toBeInTheDocument();

      fireEvent.click(screen.getByRole('button', {name: 'close selected events panel'}));
      expect(mockClearSelectedFieldEvents).toHaveBeenCalled();
    });
  });
});
