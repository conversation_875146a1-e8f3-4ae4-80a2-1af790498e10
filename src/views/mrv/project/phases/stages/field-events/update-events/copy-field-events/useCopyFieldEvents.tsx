import {useMutation, useQuery} from '@apollo/client';
import {useMemo} from 'react';
import {useIntl} from 'react-intl';

import {useAppDispatch} from 'store/useRedux';

import type {CopyFieldEventsMode} from '__generated__/gql/graphql';
import {showNotification} from '_common/components/NotificationSnackbar';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';

import {BULK_COPY_FIELD_EVENTS} from 'views/mrv/graphql/mutations/field-events';
import {GET_ALL_STAGE_EVENTS} from 'views/mrv/graphql/queries/field-events';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {refetchFieldEvents} from 'views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

export type CopyFieldEvents = (copyMode: CopyFieldEventsMode) => Promise<void>;
type UseCopyFieldEvents = (props: {selectedFieldIds: Array<string>; onComplete?: () => void}) => {
  copyFieldEvents: CopyFieldEvents;
  copyFieldEventsLoading: boolean;
};

export const useCopyFieldEvents: UseCopyFieldEvents = ({selectedFieldIds, onComplete}) => {
  const {formatMessage} = useIntl();
  const dispatch = useAppDispatch();
  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const {currentPhase} = usePhaseContext();
  const {currentStage} = useStageContext();
  const {selectedFieldEvents} = useSelectedFieldContext();

  const stageId = String(currentStage?.id);

  const refetchEvents = useMemo(
    () => refetchFieldEvents(dispatch, projectId, stageId),
    [dispatch, projectId, stageId]
  );

  const {
    data: stageEventsData,
    loading: stageEventsLoading,
    error: stageEventsError,
  } = useQuery(GET_ALL_STAGE_EVENTS, {
    variables: {
      projectId,
      fieldId: String(selectedFieldEvents.field?.id),
      stageId,
    },
    skip: !selectedFieldEvents.field?.id || !currentStage,
    fetchPolicy: 'network-only',
    nextFetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
  });

  const [bulkCopyFieldEvents, {loading: copyFieldEventsLoading}] = useMutation(
    BULK_COPY_FIELD_EVENTS,
    {
      onError: () => {
        showNotification({
          message: formatMessage({
            id: 'copyFieldEvents.confirm.saveError',
            defaultMessage: 'There was an issue copying your field events',
          }),
          type: 'error',
        });
      },
      ...refetchEvents,
    }
  );

  const copyFieldEvents = async (copyMode: CopyFieldEventsMode) => {
    if (!selectedFieldEvents.events || !currentPhase || !currentStage) {
      return;
    }

    const allEventIds = Object.values(selectedFieldEvents.events).flat();

    const events = selectedFieldIds.flatMap(targetFieldId =>
      allEventIds.map(sourceEventId => ({
        sourceEventId: String(sourceEventId),
        targetFieldId: parseInt(targetFieldId, 10),
        targetProjectId: parseInt(projectId, 10),
        targetPhaseId: parseInt(currentPhase.id, 10),
        targetStageId: parseInt(currentStage.id, 10),
      }))
    );

    const result = await bulkCopyFieldEvents({
      variables: {
        events,
        copyMode,
      },
    });

    // Close the dialog on success
    if (result.data?.bulkCopyFieldEvents.length && onComplete) {
      onComplete();
    }
  };

  return {
    copyFieldEvents,
    copyFieldEventsLoading,
  };
};
